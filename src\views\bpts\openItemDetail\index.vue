<template>
  <div class="category-container open-item-list-container">
    <el-card class="mt5" shadow="never" :style="{ height: '100%' }">
      <div class="fullscreen-box open-item-content"
        :style="{ padding: state.isFullscreen ? '20px' : '0px', height: '100%' }" ref="fullscreenElement">
        <el-header class="table_header">
          <div class="left-panel">
            <el-button type="primary" size="small" @click="onAdd" v-auth="'OpenItemList.Create'">
              <el-icon>
                <ele-Plus />
              </el-icon>{{ $t('message.openItemButtons.createOpenItem') }}
            </el-button>
            <div style="margin-left: 15px;"></div>
            <el-select-v2 ref="selectItemRef" v-model="state.formTable.params.openItemId" filterable
              :placeholder="$t('message.page.selectKeyPlaceholder')" class="w-30 openItemSelect"
              :options="state.itemOpts" size="default" @change="onSelectChange" suffix-icon="ele-Edit">
              <template #default="{ item }">
                <span>{{ item.label }}
                  <!-- <el-icon style="margin-left: 10px;" @click.stop="onEdit(item)"><ele-Edit /></el-icon>
						<el-icon style="margin-left: 10px;color:red" @click.stop="onEdit(item)"><ele-Delete /></el-icon> -->
                  <vxe-button style="margin-left: 10px;" mode="text" status="primary" icon="vxe-icon-edit"
                    v-auth="'OpenItemList.EditOpenItemFile'" @click.stop="onEdit(item)"></vxe-button>
                  <vxe-button mode="text" status="error" icon="vxe-icon-delete"
                    v-auth="'OpenItemList.DeleteOpenItemFile'" @click.stop="onDelete(item)"></vxe-button>
                </span>
              </template>
            </el-select-v2>
            <div style="margin-left: 15px;"></div>
            <el-button type="primary" size="small" @click="onSave" v-auth="'OpenItemList.Create'">
              <el-icon>
                <ele-Edit />
              </el-icon>{{ $t('message.openItemButtons.saveOpenItem') }}
            </el-button>
            <el-button type="default" size="small" @click="onCancel" v-auth="'OpenItemList.Create'">
              <el-icon>
                <ele-RefreshLeft />
              </el-icon>{{ $t('message.openItemButtons.cancelOpenItem') }}
            </el-button>
            <el-button type="primary" size="small" @click="toggleFullscreen">
              <div v-if="!state.isFullscreen"><i class="vxe-icon-zoom-out"></i>
                {{ $t('message.page.fullScreen') }}</div>
              <div v-else><i class="vxe-icon-zoom-in"></i>
                {{ $t('message.page.shrinkScreen') }}</div>
            </el-button>
            <el-tag class="ml-2 ml10" type="danger">*Tip: {{ $t('message.openItemDlgTips.detailTooltip') }}</el-tag>
            <!-- <div style="margin-left: 15px;"></div>
				<el-icon size="15" title="Full screen" style="cursor: pointer" @click="toggleFullscreen"><ele-FullScreen /></el-icon> -->
          </div>
          <div class="right-panel" v-auth="'OpenItemList.Export'">
            <el-button type="warning" size="small" @click="clearAllFilters" class="mr10">
              <el-icon>
                <ele-RefreshLeft />
              </el-icon>
              {{ $t('message.openItemFilters.clearAllFilters') }}
            </el-button>
            <el-dropdown>
              <el-button type="primary" size="small" class="ml10">
                <el-icon>
                  <ele-ArrowDownBold />
                </el-icon>
                {{ $t('message.page.buttonExport') }}
              </el-button>
              <template #dropdown>
                <el-dropdown-menu class="user-dropdown">
                  <el-dropdown-item @click="onExportAllRecord(0)">{{
                    $t('message.page.buttonExportEntireList') }}</el-dropdown-item>
                  <el-dropdown-item @click="onExportAllRecord(1)">{{
                    $t('message.page.buttonExportSelectedRecords') }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <div class="table-wrapper">
          <vxe-table show-header-overflow border :height="'auto'" ref="tableRef" keep-source
            :row-config="{ isHover: true, resizable: true }" :header-rows="{ height: 40 }"
            :column-config="{ resizable: true }" :checkbox-config="{ highlight: true }"
            :loading="state.formTable.loading" :loading-config="{ text: $t('message.page.loading') }"
            :scroll-x="{ enabled: false }" :scroll-y="{ enabled: false }" :data="state.formTable.data"
            :edit-config="editConfig" :sortConfig="sortConfig" :header-cell-style="headerCellStyle"
            :tooltip-config="tooltipConfig" @sort-change="onSortChange" @cell-dblclick="cellDblClick"
            @edit-activated="handleEditActivated" @edit-closed="handleEditClosed">

            <template #empty>
              <el-empty :description="$t('message.page.emptyDescription')" :image-size="100"></el-empty>
            </template>
            <vxe-column fixed="left" type="checkbox" width="50"></vxe-column>
            <vxe-column fixed="left" type="seq" :title="$t('message.openItemFields.Serial')" width="auto"></vxe-column>
            <vxe-column :title="$t('message.openItemFields.SortId')" field="sortId" width="auto"
              v-if="false"></vxe-column>
            <vxe-column fixed="left" :title="$t('message.openItemFields.TicketNumber')" field="ticketNumber"
              width="auto" sortable :filters="state.dataColumns.ticketList">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchTicketText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <vxe-link @click="onEditTicket(row.ticketNumber)" status="primary">
                  {{ row.ticketNumber }}
                </vxe-link>
              </template>
            </vxe-column>
            <vxe-column fixed="left" :title="$t('message.openItemFields.Priority')" field="priority" width="120"
              sortable :filters="state.dataColumns.priorityList" :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchPriorityText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #edit="{ row }">
                <el-select-v2 v-model="row.priority" clearable :placeholder="$t('message.page.selectKeyPlaceholder')"
                  class="w-20" :options="state.priorityOpts" size="default">
                </el-select-v2>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.Project')" field="projectName" width="150" sortable
              :filters="state.dataColumns.projectList" :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchProjectText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ row.projectName }}</span>
              </template>
              <template #edit="{ row }">
                <el-select-v2 v-model="row.project" clearable :placeholder="$t('message.page.selectKeyPlaceholder')"
                  class="w-20" :options="state.projectOpts" size="default"
                  @change="(value: any) => onProjectChange(value, row)">
                </el-select-v2>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.TaskCategory')" field="taskCategory" width="auto" sortable
              :filters="state.dataColumns.taskCategoryList" :edit-render="{}">
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchTaskCategoryText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ row.taskCategory }}</span>
              </template>
              <template #edit="{ row }">
                <el-select-v2 v-model="row.taskCategory" clearable
                  :placeholder="$t('message.page.selectKeyPlaceholder')" class="task-category-select"
                  :options="state.taskCategoryOpts" size="default">
                </el-select-v2>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.Description')" field="description" width="250" sortable
              :filters="state.dataColumns.descriptionList" :edit-render="{}">
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchDescriptionText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <div style="display: flex; align-items: center;">
                  <div v-html="row.description"></div>
                </div>
              </template>
              <template #edit="{ row }">
                <ElInput type="textarea" size="small" class="descTextarea" :autosize="{ minRows: 2, maxRows: 100 }"
                  v-model="row.description" @input="syncFixedColumnHeight"></ElInput>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.Comment')" field="comment" width="250" sortable
              :filters="state.dataColumns.commentList" :edit-render="{}">
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchCommentText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <!-- 整体容器 -->
                <div style="display: flex; align-items: center;">
                  <div style="display: flex; flex-direction: column; justify-content: center;">
                    <!-- 提示信息 -->
                    <div v-if="row.commentCreatedAt" style="font-weight: bold;"
                      v-html="row.commentCreatedAt + ' ' + row.commentCreatedBy"></div>
                    <!-- 内容 -->
                    <div style="padding: 0 15px 0 0;" v-html="row.comment"></div>
                  </div>
                  <!-- 图标 -->
                  <i class="vxe-icon-feedback custom-icon" @click="handleSuffixClick(row)"></i>
                </div>
              </template>
              <template #edit="{ row }">
                <!-- 整体容器 -->
                <div style="display: flex; flex-direction: column; justify-content: center;">
                  <!-- 提示信息 -->
                  <div v-if="row.commentCreatedAt" style="font-weight: bold;"
                    v-html="row.commentCreatedAt + ' ' + row.commentCreatedBy"></div>
                  <!-- 编辑模式和图标 -->
                  <div class="textarea-container" style="display: flex; align-items: center;">
                    <ElInput type="textarea" size="small" :autosize="{ minRows: 2, maxRows: 100 }"
                      class="commentTextarea" v-model="row.comment" @input="syncFixedColumnHeight"></ElInput>
                    <i class="vxe-icon-feedback custom-icon" @click="handleSuffixClick(row)"></i>
                  </div>
                </div>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.Status')" field="status" width="150" sortable
              :filters="state.dataColumns.statusList" :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchStatusText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ row.status }}</span>
              </template>
              <template #edit="{ row }">
                <el-select-v2 v-model="row.status" clearable :disabled="state.isStatusDisabled"
                  :placeholder="$t('message.page.selectKeyPlaceholder')" class="w-20" :options="state.statusOpts"
                  size="default" @change="(value: any) => onStatusChange(value, row)">
                </el-select-v2>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.OpenDate')" field="openDate" width="auto" sortable
              :filters="state.dataColumns.openDateList" :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchOpenDateText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ getComputedDate(row.openDate).value }}</span>
              </template>
              <template #edit="{ row }">
                <MyDate v-model:input="row.openDate" :placeholder="$t('message.page.selectKeyPlaceholder')">
                </MyDate>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.CloseDate')" field="closeDate" width="auto" sortable
              :filters="state.dataColumns.closeDateList" :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchCloseDateText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ getComputedDate(row.closeDate).value }}</span>
              </template>
              <template #edit="{ row }">
                <MyDate v-model:input="row.closeDate" :placeholder="$t('message.page.selectKeyPlaceholder')">
                </MyDate>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.LOE')" field="loe" width="100" sortable
              :filters="state.dataColumns.loeList" :edit-render="{ name: 'VxeInput', props: { type: 'number' } }">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchLoeText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ row.loe }}</span>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.ApprovedDate')" field="approvedDate" width="auto" sortable
              :filters="state.dataColumns.approvedDateList" :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchApprovedDateText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <span>{{ getComputedDate(row.approvedDate).value }}</span>
              </template>
              <template #edit="{ row }">
                <MyDate v-model:input="row.approvedDate" :placeholder="$t('message.page.selectKeyPlaceholder')">
                </MyDate>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.PointOfContact')" field="pointOfContact" width="auto"
              sortable :filters="state.dataColumns.pointofContactList" :edit-render="{ name: 'ElInput' }">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchPointOfContactText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.Update')" field="updates" width="auto" sortable
              :filters="state.dataColumns.updateList" :edit-render="{ name: 'ElInput' }">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchUpdateText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.RelatedTickets')" field="relatedTicketIds" width="auto"
              sortable :filters="state.dataColumns.relatedTicketList" :filter-method="filterRelatedTickets"
              :edit-render="{}">
              <!-- 筛选框 -->
              <template #filter="{ column, $panel }">
                <div class="my-filter-content">
                  <div class="my-fc-search">
                    <div class="my-fc-search-top">
                      <!-- 搜索框 -->
                      <vxe-input v-model="state.filterTexts.searchRelatedTicketText"
                        :placeholder="$t('message.page.searchKeyPlaceholder')" suffix-icon="fa fa-search"></vxe-input>
                    </div>
                    <div class="my-fc-search-content">
                      <!-- 筛选列表 -->
                      <template v-if="filteredEventList(column).length">
                        <ul class="my-fc-search-list my-fc-search-list-head">
                          <li class="my-fc-search-item">
                            <vxe-checkbox v-model="state.isAllSelect" :indeterminate="isIndeterminate(column)"
                              :checked="isAllChecked(column)" @change="changeAllEvent(column)">{{
                                $t('message.openItemFilters.allFilter') }}</vxe-checkbox>
                          </li>
                        </ul>
                        <ul class="my-fc-search-list my-fc-search-list-body">
                          <li class="my-fc-search-item" v-for="(item, sIndex) in filteredEventList(column)"
                            :key="sIndex">
                            <vxe-checkbox v-model="item.checked" :title="item.label">{{ item.label }}</vxe-checkbox>
                          </li>
                        </ul>
                      </template>
                    </div>
                  </div>
                  <div class="my-fc-footer">
                    <vxe-button type="text" @click="(value: any) => confirmFilterEvent(value, column)">{{
                      $t('message.openItemFilters.confirmFilter') }}</vxe-button>
                    <vxe-button style="margin-left: 0" type="text"
                      @click="(value: any) => resetFilterEvent(value, column)">{{
                        $t('message.openItemFilters.resetFilter') }}</vxe-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <vxe-link v-for="(item, sIndex) in row.relatedTicketNumbers.split(',')" :key="sIndex" status="primary">
                  <span @click="onEditTicket(item)">{{ item }}</span><span
                    v-if="sIndex != row.relatedTicketNumbers.split(',').length - 1">,</span>
                </vxe-link>
              </template>
              <template #edit="{ row }">
                <el-tree-select v-model="row.relatedTicketIds" :data="state.ticketOpts" node-key="value" multiple
                  :render-after-expand="false" filterable clearable show-checkbox :check-on-click-node="true"
                  :check-strictly="true" :default-expand-all="true" class="w-20 tag-select-input" collapse-tags
                  :placeholder="$t('message.page.selectKeyPlaceholder')" :loading="state.ticketLoading"
                  loading-text="Loading..." @update:modelValue="(value: any) => onTicketChange(value, row)">
                  <template #loading>
                    <div class="custom-loading">
                      <el-icon class="is-loading">
                        <Loading />
                      </el-icon>
                      <span>Loading...</span>
                    </div>
                  </template>
                </el-tree-select>
              </template>
            </vxe-column>
            <vxe-column :title="$t('message.openItemFields.Attachment')" field="attachmentName" width="220">
              <template #default="{ row }">
                <vxe-upload v-model="row.attachmentName" show-download-button url-mode single-mode show-progress
                  size="mini" :upload-method="uploadMethod(row)" :download-method="downloadMethod(row)"
                  :before-remove-method="beforeRemoveMethod(row)" :remove-method="removeMethod(row)">
                </vxe-upload>
              </template>
            </vxe-column>
            <vxe-column fixed="right" :title="$t('message.page.actions')" width="100">
              <template #default="scope">
                <el-tooltip class="box-item" effect="dark" :content="$t('message.limits.AddItem')" placement="bottom">
                  <vxe-button mode="text" status="primary" icon="vxe-icon-add" v-auth="'OpenItemList.AddItem'"
                    @click="onAddItem(scope)"></vxe-button>
                </el-tooltip>
                <el-tooltip class="box-item" effect="dark" :content="$t('message.limits.Delete')" placement="bottom">
                  <vxe-button mode="text" status="error" icon="vxe-icon-delete" v-auth="'OpenItemList.Delete'"
                    @click="onDeleteItem(scope)"></vxe-button>
                </el-tooltip>
              </template>
            </vxe-column>
          </vxe-table>
          <div class="scTable-page">
            <el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :pager-count="5"
              :page-sizes="[15, 20, 30, 1000]" v-model:current-page="state.formTable.params.pageIndex" background
              v-model:page-size="state.formTable.params.pageSize" layout="total" :total="state.formTable.total" small>
            </el-pagination>
          </div>
        </div>
        <createOrEdit ref="createOrEditRef" :title="state.fromTitle" @refreshData="onCreateItemInit" />
        <createTicket ref="createTicketRef" :title="state.fromTitle" @refreshData="onSearch" />
        <commentView ref="commentViewRef" :title="state.fromTitle"></commentView>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, computed, reactive, onMounted, nextTick, defineAsyncComponent, onActivated, onDeactivated, getCurrentInstance, h } from 'vue';
import { ElMessageBox, ElMessage, ElSelectV2, ElInput } from 'element-plus';
import { formatStrDate, formatDateTime, formatDay } from '/@/utils/formatTime';
import { Loading } from '@element-plus/icons-vue'; // 引入加载图标
import type { TableInstance, Action } from 'element-plus';
import { getElcascaderSingle, parseThanZero, isNotEmptyOrNull, getYearFromDate, handleError } from '/@/utils';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import openItemApi from '/@/api/cmOpenItem/index';
import openItemDetailApi from '/@/api/cmOpenItemDetail/index';
import openItemDetailAttachmentApi from '/@/api/cmOpenItemDetailAttachment/index';
import dictItemApi from '/@/api/dictItem/index';
import openItemProjectApi from '/@/api/cmOpenItemProject/index';
import cmTicketsApi from '/@/api/cmTickets/index';
//import Export from '/@/components/export/index.vue';
import { VxeTablePropTypes, VxeTableEvents, VxeGridInstance, VxeColumnPropTypes } from 'vxe-table';
import { formData } from '../../pages/dynamicForm/mock';
import { Console } from 'console';
import Auth from '/@/components/auth/auth.vue';
import screenfull from 'screenfull';
import { VxeUI, VxeUploadProps, VxeUploadPropTypes } from 'vxe-pc-ui';
import request from '/@/utils/request';
import { encodeTicketNumber } from '/@/utils';
import { Plus } from '@element-plus/icons-vue';
import type { UploadProps, UploadUserFile } from 'element-plus';
import config from '/@/config/upload.js';
import { waitForDebugger } from 'inspector';

const createOrEdit = defineAsyncComponent(() => import('./components/createOrEdit.vue'));
const createTicket = defineAsyncComponent(() => import('./components/createTicket.vue'));
const commentView = defineAsyncComponent(() => import('./components/commentView.vue'));
const MyDate = defineAsyncComponent(() => import('/@/components/ticket/ticketDate.vue'));

const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const createOrEditRef = ref();
const createTicketRef = ref();
const commentViewRef = ref();
const selectItemRef = ref();
const uniqueValuesCounts = reactive<Record<string, { value: any; count: number }[]>>({});
const fullscreenElement = ref<HTMLElement | null>(null);
const BASE_URL = computed(() => {
  const appSettings = proxy?.$appSettings;
  return appSettings?.BASE_URL || import.meta.env.VITE_API_URL;
});
//   const exportRef = ref();

interface RowVO {
  id: number;
  openItemId: string;
  createby: number;
  createat: string;
  modifyby: string;
  modifyat: string;
  status: string;
  openDate: Date;
  closeDate: Date;
  approvedDate: Date;
  loe: number;
  relatedTickets: {}[];
  relatedTicketNumbers: string;
  relatedTicketIds: number[];
}
const tableRef = ref<VxeGridInstance<RowVO>>();
const editConfig = reactive<VxeTablePropTypes.EditConfig<RowVO>>({
  trigger: 'dblclick',
  mode: 'row',
  showIcon: false,
  autoClear: false,
  showStatus: true,
});
const sortConfig = ref<VxeTablePropTypes.SortConfig<RowVO>>({
  defaultSort: {
    field: 'sortId',
    order: 'asc',
  },
});

// #region filter事件
// Filter状态持久化的key
const FILTER_STORAGE_KEY = 'openItemDetail_filterState';

// 保存filter状态到localStorage
const saveFilterState = () => {
  try {
    const filterState = {
      filterTexts: state.filterTexts,
      dataColumnsChecked: {} as any,
      tableFilters: {} as any
    };

    // 保存每列的选中状态
    Object.keys(state.dataColumns).forEach(key => {
      const columnData = state.dataColumns[key as keyof typeof state.dataColumns];
      if (Array.isArray(columnData)) {
        filterState.dataColumnsChecked[key] = columnData.map(item => ({
          value: item.value,
          label: item.label,
          checked: item.checked || false
        }));
      }
    });

    // 保存表格的filter状态
    const $table = tableRef.value;
    if ($table) {
      const checkedFilters = $table.getCheckedFilters();
      filterState.tableFilters = checkedFilters.reduce((acc: any, filter: any) => {
        acc[filter.field] = filter.values;
        return acc;
      }, {});
    }

    localStorage.setItem(FILTER_STORAGE_KEY, JSON.stringify(filterState));
  } catch (error) {
    console.warn('Failed to save filter state:', error);
  }
};

// 从localStorage恢复filter状态
const restoreFilterState = () => {
  try {
    const savedState = localStorage.getItem(FILTER_STORAGE_KEY);
    if (!savedState) return false;

    const filterState = JSON.parse(savedState);

    // 恢复搜索文本
    if (filterState.filterTexts) {
      Object.assign(state.filterTexts, filterState.filterTexts);
    }

    // 恢复选中状态
    if (filterState.dataColumnsChecked) {
      Object.keys(filterState.dataColumnsChecked).forEach(key => {
        const columnData = state.dataColumns[key as keyof typeof state.dataColumns];
        const savedChecked = filterState.dataColumnsChecked[key];

        if (Array.isArray(columnData) && Array.isArray(savedChecked)) {
          columnData.forEach(item => {
            const savedItem = savedChecked.find((saved: any) => saved.value === item.value);
            if (savedItem) {
              item.checked = savedItem.checked;
            }
          });
        }
      });
    }

    // 恢复表格filter状态
    if (filterState.tableFilters) {
      nextTick(() => {
        const $table = tableRef.value;
        if ($table) {
          Object.keys(filterState.tableFilters).forEach(field => {
            const values = filterState.tableFilters[field];
            if (values && values.length > 0) {
              $table.setFilter(field, getFilterList({ property: field }), true);
            }
          });
        }
      });
    }

    return true;
  } catch (error) {
    console.warn('Failed to restore filter state:', error);
    return false;
  }
};

// 清除filter状态
const clearFilterState = () => {
  try {
    localStorage.removeItem(FILTER_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear filter state:', error);
  }
};

// 清除所有filter的方法
const clearAllFilters = () => {
  // 清除所有搜索文本
  Object.keys(state.filterTexts).forEach(key => {
    (state.filterTexts as any)[key] = '';
  });

  // 清除所有选中状态
  Object.keys(state.dataColumns).forEach(key => {
    const columnData = state.dataColumns[key as keyof typeof state.dataColumns];
    if (Array.isArray(columnData)) {
      columnData.forEach(item => {
        item.checked = false;
      });
    }
  });

  // 清除表格filter
  const $table = tableRef.value;
  if ($table) {
    $table.clearFilter();
  }

  // 清除localStorage中的状态
  clearFilterState();
};

const isAllChecked = computed(() => (column: any) => {
  state.isAllSelect = filteredEventList(column).every((item) => item.checked);
  return state.isAllSelect;
});

const isIndeterminate = computed(() => (column: any) => {
  return filteredEventList(column).some((item) => item.checked) && !isAllChecked.value(column);
});

const filteredEventList = (column: any) => {
  return getFilterList(column).filter((item: any) => item.value.toLowerCase().includes(getSearchFilterText(column).toLowerCase()));
};
//获取对应的筛选列表
const getFilterList = (column: any) => {
  var filterList: any[] = [];
  //根据列属性获取对应的筛选列表
  if (column.property === 'ticketNumber') {
    filterList = state.dataColumns.ticketList;
  } else if (column.property === 'priority') {
    filterList = state.dataColumns.priorityList;
  } else if (column.property === 'projectName') {
    filterList = state.dataColumns.projectList;
  } else if (column.property === 'taskCategory') {
    filterList = state.dataColumns.taskCategoryList;
  } else if (column.property === 'description') {
    filterList = state.dataColumns.descriptionList;
  } else if (column.property === 'comment') {
    filterList = state.dataColumns.commentList;
  } else if (column.property === 'status') {
    filterList = state.dataColumns.statusList;
  } else if (column.property === 'openDate') {
    filterList = state.dataColumns.openDateList;
  } else if (column.property === 'closeDate') {
    filterList = state.dataColumns.closeDateList;
  } else if (column.property === 'loe') {
    filterList = state.dataColumns.loeList;
  } else if (column.property === 'approvedDate') {
    filterList = state.dataColumns.approvedDateList;
  } else if (column.property === 'pointOfContact') {
    filterList = state.dataColumns.pointofContactList;
  } else if (column.property === 'updates') {
    filterList = state.dataColumns.updateList;
  } else if (column.property === 'relatedTicketIds') {
    filterList = state.dataColumns.relatedTicketList;
  }
  return filterList;
};
//获取对应的搜索文本
const getSearchFilterText = (column: any) => {
  var searchFilterText: string = '';
  //根据列属性获取对应的搜索文本
  if (column.property === 'ticketNumber') {
    searchFilterText = state.filterTexts.searchTicketText;
  } else if (column.property === 'priority') {
    searchFilterText = state.filterTexts.searchPriorityText;
  } else if (column.property === 'projectName') {
    searchFilterText = state.filterTexts.searchProjectText;
  } else if (column.property === 'taskCategory') {
    searchFilterText = state.filterTexts.searchTaskCategoryText;
  } else if (column.property === 'description') {
    searchFilterText = state.filterTexts.searchDescriptionText;
  } else if (column.property === 'comment') {
    searchFilterText = state.filterTexts.searchCommentText;
  } else if (column.property === 'status') {
    searchFilterText = state.filterTexts.searchStatusText;
  } else if (column.property === 'openDate') {
    searchFilterText = state.filterTexts.searchOpenDateText;
  } else if (column.property === 'closeDate') {
    searchFilterText = state.filterTexts.searchCloseDateText;
  } else if (column.property === 'loe') {
    searchFilterText = state.filterTexts.searchLoeText;
  } else if (column.property === 'approvedDate') {
    searchFilterText = state.filterTexts.searchApprovedDateText;
  } else if (column.property === 'pointOfContact') {
    searchFilterText = state.filterTexts.searchPointOfContactText;
  } else if (column.property === 'updates') {
    searchFilterText = state.filterTexts.searchUpdateText;
  } else if (column.property === 'relatedTicketIds') {
    searchFilterText = state.filterTexts.searchRelatedTicketText;
  }
  return searchFilterText;
};
// 清空对应的搜索文本
const clearSearchFilterText = (column: any) => {
  if (column.property === 'ticketNumber') {
    state.filterTexts.searchTicketText = '';
  } else if (column.property === 'priority') {
    state.filterTexts.searchPriorityText = '';
  } else if (column.property === 'projectName') {
    state.filterTexts.searchProjectText = '';
  } else if (column.property === 'taskCategory') {
    state.filterTexts.searchTaskCategoryText = '';
  } else if (column.property === 'description') {
    state.filterTexts.searchDescriptionText = '';
  } else if (column.property === 'comment') {
    state.filterTexts.searchCommentText = '';
  } else if (column.property === 'status') {
    state.filterTexts.searchStatusText = '';
  } else if (column.property === 'openDate') {
    state.filterTexts.searchOpenDateText = '';
  } else if (column.property === 'closeDate') {
    state.filterTexts.searchCloseDateText = '';
  } else if (column.property === 'loe') {
    state.filterTexts.searchLoeText = '';
  } else if (column.property === 'approvedDate') {
    state.filterTexts.searchApprovedDateText = '';
  } else if (column.property === 'pointOfContact') {
    state.filterTexts.searchPointOfContactText = '';
  } else if (column.property === 'updates') {
    state.filterTexts.searchUpdateText = '';
  } else if (column.property === 'relatedTicketIds') {
    state.filterTexts.searchRelatedTicketText = '';
  }
};
// 全选/取消全选事件
const changeAllEvent = (column: any) => {
  getFilterList(column).forEach((item) => {
    item.checked = state.isAllSelect;
  });
};

// 确认筛选事件
const confirmFilterEvent = (evnt: Event, column: any) => {
  const $table = tableRef.value;
  if ($table) {
    $table.setFilter(column.property, getFilterList(column), true);
    $table.closeFilter();
    // 保存filter状态
    saveFilterState();
  }
};

// 重置筛选事件
const resetFilterEvent = (evnt: Event, column: any) => {
  const $table = tableRef.value;
  if ($table) {
    clearSearchFilterText(column);
    getFilterList(column).forEach((item: any) => {
      item.checked = false;
    });
    $table.clearFilter(column.property);
    $table.closeFilter();
    // 保存filter状态
    saveFilterState();
  }
};

// 自定义事件
const triggerCustomEvent = (column: any) => {
  const $table = tableRef.value;
  if ($table) {
    if (!$table.isActiveFilterByColumn(column.property)) {
      clearSearchFilterText(column);
      getFilterList(column).forEach((item: any) => {
        item.checked = false;
      });
    }
  }
};

// 监听筛选图标的点击事件
const setupFilterIconListener = () => {
  const $table = tableRef.value;
  if ($table) {
    // 获取表格的根元素
    const tableEl = $table.$el;

    // 监听表格内的点击事件
    tableEl.addEventListener('click', (event: any) => {
      // 检查点击的是否是筛选图标
      const filterIcon = event.target.closest('.vxe-cell--filter .vxe-filter--btn');
      if (filterIcon) {
        // 阻止默认行为（如果需要）
        // event.preventDefault();
        const headerCell = event.target.closest('.vxe-header--column');
        if (headerCell) {
          // 获取列的配置信息
          const columnProp = headerCell.getAttribute('colid'); // 获取列的 colid
          const column = $table.getColumnById(columnProp); // 根据 colid 获取列配置

          if (column) {
            // 在 nextTick 中确保 DOM 更新完成
            nextTick(() => {
              // 触发自定义事件，并传递列信息
              triggerCustomEvent(column);
            });
          }
        }
      }
    });
  }
};
//#endregion

// 提示框配置
const tooltipConfig = ref({
  showAll: true, // 显示所有内容
  enterable: true, // 允许鼠标进入提示框
  enterDelay: 100,
  contentMethod: ({ row, rowIndex, column }: any) => {
    // if(!column.title || column.title === 'Actions' || column.title === 'Item#' || column.title === 'Ticket#' || column.title === 'Attachment'){
    // 	return "";
    // }
    // else if(row){
    // 	return t('message.openItemDlgTips.detailTooltip');
    // }
    //表内容不需要提示框
    if (column.title && row) {
      return '';
    }
  },
});

const uploadMethod = (row: any) => {
  return ({ file }: any) => {
    const formData = new FormData();
    formData.append('OpenItemDetailId', row.id);
    formData.append('file', file);
    console.log('uploadMethod', row);
    const $table = tableRef.value;

    // 使用统一的 request 实例，并设置正确的 Content-Type
    return request.post('Api/CmOpenItemDetailAttachment/UpLoadV2', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }).then((res) => {
      // { url: ''}
      if ($table) {
        setTimeout(() => {
          $table.recalculate();
        }, 0);
      }
      return {
        ...res.data,
      };
    });
  };
};

const downloadMethod = (row: any) => {
  return ({ option }: any) => {
    openItemDetailAttachmentApi
      .Detail(row.id)
      .then(async (rs) => {
        const blob = await fetch(`${BASE_URL.value}` + rs.data.filePath).then((response) => response.blob());
        await VxeUI.saveFile({
          filename: option.name,
          content: blob,
        });
        /*
        VxeUI.modal.message({
          content: 'Successfully Download',
          status: 'success',
        });
        */
      })
      .catch((rs) => {
        VxeUI.modal.message({
          content: 'Error Download',
          status: 'error',
        });
      });
  };
};
// 删除前的确认方法
const beforeRemoveMethod = (row: any) => {
  return () => {
    return ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
      confirmButtonText: t('message.page.confirm'),
      cancelButtonText: t('message.page.cancel'),
      type: 'warning',
    })
      .then(() => {
        return true; // 用户确认删除
      })
      .catch(() => {
        return false; // 用户取消删除
      });
  };
};

// 删除方法
const removeMethod = (row: any) => {
  return () => {
    // 用户确认删除，执行删除操作
    openItemDetailAttachmentApi
      .DeleteByKey(row.id)
      .then(() => {
        VxeUI.modal.message({
          content: 'Successfully Deleted',
          status: 'success',
        });
      })
      .catch(() => {
        VxeUI.modal.message({
          content: 'Error Deleted',
          status: 'error',
        });
      });
  };
};

const filterRelatedTickets = ({ option, row }: { option: any; row: any }) => {
  // option 是筛选条件，row 是当前行数据
  return row.relatedTicketNumbers === option.value;
};

const handleSuffixClick = (params: any) => {
  console.log('Suffix icon clicked!', params);
  // 在这里添加处理逻辑
  state.fromTitle = t('message.openItemButtons.viewComment');
  commentViewRef.value.openDialog(params);
};

const toggleFullscreen = async () => {
  const element = proxy.$refs.fullscreenElement;
  state.isFullscreen = !state.isFullscreen;
  if (state.isFullscreen) {
    element.style.position = 'fixed';
    element.style.top = '0';
    element.style.left = '0';
    element.style.width = '100vw';
    element.style.height = '100vh';
    element.style.zIndex = '1000';
    element.style.overflow = 'auto';
    element.style.backgroundColor = '#fff';
  } else {
    element.style.position = 'relative';
    element.style.top = 'auto';
    element.style.left = 'auto';
    element.style.width = 'auto';
    element.style.height = '100%';
    element.style.zIndex = 'auto';
    element.style.overflow = 'auto';
    element.style.backgroundColor = 'transparent';
  }
  await updateMaxHeight();
};

const handleEditActivated: VxeTableEvents.EditActivated<RowVO> = async ({ row }) => {
  const $table = tableRef.value;
  if ($table) {
    // 刷新表格的滚动区域和固定列高度
    setTimeout(() => {
      $table.recalculate();
    }, 0);
    // $table.refreshScroll();
    // 等待 DOM 更新完成
    // await nextTick();
  }
};
const handleEditClosed: VxeTableEvents.EditClosed<RowVO> = async ({ row }) => {
  // 关闭编辑行时触发，可用于保存row数据
  console.log('onEditClosed', row);
  if (row.relatedTicketIds.length > 0) {
    row.relatedTickets = [];
    for (var ticketId of row.relatedTicketIds) {
      var ticket = processRelatedTickets(state.ticketOpts, ticketId, row);
      if (ticket && !row.relatedTickets.some((ticket: any) => ticket.ticketId === ticket.value)) {
        row.relatedTickets.push({ ticketId: ticket.value, ticketNumber: ticket.label });
        row.relatedTicketNumbers = row.relatedTickets.map((ticket: any) => ticket.ticketNumber).join(',');
      }
    }
  }
  const $table = tableRef.value;
  if ($table) {
    setTimeout(() => {
      $table.recalculate();
    }, 0);
  }
};

let debounceTimer: NodeJS.Timeout | null = null;
const syncFixedColumnHeight = () => {
  clearTimeout(debounceTimer as NodeJS.Timeout);
  debounceTimer = setTimeout(() => {
    const $table = tableRef.value;
    if ($table) {
      $table.recalculate();
    }
  }, 200);
};

const onTicketChange = (value: number[], row: RowVO) => {
  if (value.length > 0) {
    row.relatedTickets = [];
    for (var ticketId of value) {
      var ticket = processRelatedTickets(state.ticketOpts, ticketId, row);
      if (ticket && !row.relatedTickets.some((ticket: any) => ticket.ticketId === ticket.value)) {
        row.relatedTickets.push({ ticketId: ticket.value, ticketNumber: ticket.label });
        row.relatedTicketNumbers = row.relatedTickets.map((ticket: any) => ticket.ticketNumber).join(',');
      }
    }
    console.log(row.relatedTickets);
  }
};

function processRelatedTickets(tickets: any[], ticketId: number, row: any): any {
  for (const ticket of tickets) {
    // 如果当前对象的 ticketId 匹配，返回 name
    if (ticket.value === ticketId) {
      return ticket;
    }

    // 如果当前对象有子对象，递归查找子对象
    if (ticket.children && ticket.children.length > 0) {
      const result = processRelatedTickets(ticket.children, ticketId, row);
      if (result) {
        return result;
      }
    }
  }

  // 如果没有找到，返回 undefined
  return undefined;
}

const headerCellStyle = ({ column }: any) => {
  if (column.sortable) {
    return {
      cursor: 'pointer',
    };
  }
};

function getDefaultQueryParams() {
  return {
    pageIndex: 1,
    pageSize: 5000,
    order: 'sortId',
    sort: 'asc',
    code: '',
    name: '',
    description: '',
    status: '',
    id: 0,
    openItemId: '',
    ticketNumber: '',
    customer: '',
    ids: [],
    ischeckType: 0,
    OpenItemDetailFilters: [],
  };
}

const getOpenItemDetailFilters = () => {
  const checkedFilters = proxy.$refs.tableRef.getCheckedFilters();
  return checkedFilters.map((item: any) => ({ Field: item.field, Values: item.values }));
};

const state = reactive({
  fromTitle: '',
  maxHeight: 0,
  fullPadding: false,
  isFullscreen: false,
  isAllSelect: false,
  fileList: [] as any,
  ticketLoading: false,
  isStatusDisabled: false,
  oldRow: {} as RowVO,
  formTable: {
    data: [] as any,
    loading: false,
    total: 0,
    selection: [] as any,
    params: getDefaultQueryParams(),
  },
  filterTexts: {
    searchTicketText: '',
    searchProjectText: '',
    searchPriorityText: '',
    searchTaskCategoryText: '',
    searchDescriptionText: '',
    searchCommentText: '',
    searchStatusText: '',
    searchOpenDateText: '',
    searchCloseDateText: '',
    searchLoeText: '',
    searchApprovedDateText: '',
    searchPointOfContactText: '',
    searchUpdateText: '',
    searchRelatedTicketText: '',
  },
  dataColumns: {
    ticketList: ref<VxeColumnPropTypes.Filters>([]),
    priorityList: ref<VxeColumnPropTypes.Filters>([]),
    projectList: ref<VxeColumnPropTypes.Filters>([]),
    taskCategoryList: ref<VxeColumnPropTypes.Filters>([]),
    descriptionList: ref<VxeColumnPropTypes.Filters>([]),
    commentList: ref<VxeColumnPropTypes.Filters>([]),
    statusList: ref<VxeColumnPropTypes.Filters>([]),
    openDateList: ref<VxeColumnPropTypes.Filters>([]),
    closeDateList: ref<VxeColumnPropTypes.Filters>([]),
    loeList: ref<VxeColumnPropTypes.Filters>([]),
    approvedDateList: ref<VxeColumnPropTypes.Filters>([]),
    pointofContactList: ref<VxeColumnPropTypes.Filters>([]),
    updateList: ref<VxeColumnPropTypes.Filters>([]),
    relatedTicketList: ref<VxeColumnPropTypes.Filters>([]),
  },
  statusOpts: [],
  itemOpts: [] as any,
  projectOpts: [] as any,
  priorityOpts: [] as any,
  ticketOpts: [] as any,
  taskCategoryOpts: [] as any,
});
const tableMaxHeight = computed(() => (state.maxHeight > 0 ? state.maxHeight : 650)); // 设置默认的最大高度

// 监听搜索文本变化，实时保存filter状态
watch(() => state.filterTexts, () => {
  // 延迟保存，避免频繁操作
  setTimeout(() => {
    saveFilterState();
  }, 500);
}, { deep: true });

var isMounted = false;
onMounted(async () => {
  isMounted = true;
  if (isMounted) {
    state.formTable.loading = true;
    getOpenItemDetails(async () => {
      await getOpenItemProject();
      await onSearch();
      await onInitLoading();
      await getTickets();
    });
  }
  updateMaxHeight();
  window.addEventListener('resize', updateMaxHeight);
  setupFilterIconListener();
});
const updateMaxHeight = () => {
  // state.fullPadding=!state.fullPadding;
  // nextTick(() => {
  //   setTimeout(() => {
  //     if (state.isFullscreen) {
  //       state.maxHeight = window.innerHeight - 120;
  //     } else {
  //       state.maxHeight = window.innerHeight - 270;
  //     }
  //     // 修改筛选框高度
  //     document.documentElement.style.setProperty('--vxe-ui-upload-image-wh-default', state.maxHeight - 120 + 'px');
  //   }, 100);
  // });
};

onActivated(async () => {
  if (!isMounted) {
    isMounted = true;
    const $table = tableRef.value;

    if ($table) {
      const updateRecords = $table.getUpdateRecords();
      if (updateRecords.length == 0) {
        await onSearch();
      }
    }
  }
});

onDeactivated(() => {
  isMounted = false;
});

const onAddItem = (scope: any) => {
  var obj = {
    id: 0,
    sortId: scope.row.sortId + 1,
    openItemId: scope.row.openItemId,
    Status: '',
    OpenDate: new Date(0),
    CloseDate: new Date(0),
    ApprovedDate: new Date(0),
    TicketId: 0,
    TicketNumber: '',
    Priority: '',
    Project: 0,
    TaskCategory: '',
    Description: '',
    Comment: '',
    LOE: 0,
    PointOfContact: '',
    Updates: '',
    Customer: '',
    Projects: [] as any,
  };

  ElMessageBox.confirm(t('message.openItemDlgTips.addDetailTips'), t('message.page.dlgTip'), {
    distinguishCancelAndClose: true,
    confirmButtonText: t('message.page.confirm'),
    cancelButtonText: t('message.page.cancel'),
    type: 'warning',
  })
    .then(() => {
      state.fromTitle = t('message.openItemButtons.createOpenItem');
      // createOrEditRef.value.openDialog(getDefaultQueryParams());
      obj.Customer = state.formTable.params.customer;
      obj.Projects = state.projectOpts;
      createTicketRef.value.openDialog(obj);
    })
    .catch((action: Action) => {
      if (action === 'cancel') {
        openItemDetailApi
          .Save(obj)
          .then((rs) => {
            // state.formTable.data.splice(scope.rowIndex + 1, 0, obj)
            onSearch();
          })
          .catch((rs: any) => {
            handleError(rs);
          });
      }
    });
};
const onDeleteItem = (scope: any) => {
  if (state.formTable.data.length > 1) {

    var msg = scope.row.status == "Complete" ? t('message.openItemDlgTips.deleteOpenItemTipTextComplete') : t('message.openItemDlgTips.deleteOpenItemTipText');
    var confirmButtonMsg = scope.row.status == "Complete" ? t('message.page.buttonOk') : t('message.page.confirm');
    var cancelButtonMsg = t('message.page.cancel');

    ElMessageBox.confirm(msg, t('message.page.dlgTip'), {
      confirmButtonText: confirmButtonMsg,
      cancelButtonText: cancelButtonMsg,
      showCancelButton: scope.row.status != "Complete",
      type: 'warning',
    })
      .then(async () => {
        if (scope.row.status != "Complete") {
          openItemDetailApi
            .DeleteByKey(scope.row.id)
            .then((rs) => {
              onSearch();
            })
            .catch((rs: any) => {
              handleError(rs);
            });
        }
      })
      .catch((error: any) => { });
  } else {
    ElMessage({
      message: 'At least one row must be present.',
      type: 'warning',
    });
  }
};

const onEditTicket = (id: any) => {
  const encodedTicketNumber = encodeTicketNumber(id);
  router.push('/dashboard/editTicket/' + encodedTicketNumber);
};

const onAdd = () => {
  state.fromTitle = t('message.openItemButtons.createOpenItem');
  createOrEditRef.value.openDialog(getDefaultQueryParams());
};

const onEdit = (row: any) => {
  state.fromTitle = t('message.openItemButtons.editOpenItem');
  createOrEditRef.value.openDialog(row);
};

const onDelete = async (row: any) => {
  // selectItemRef.value.blur();
  await nextTick(); // 等待 DOM 更新完成
  ElMessageBox.confirm(t('message.page.dlgDeleteText'), t('message.page.dlgTip'), {
    confirmButtonText: t('message.page.confirm'),
    cancelButtonText: t('message.page.cancel'),
    type: 'warning',
  }).then(() => {
    // onSelectChange(row);
    // 获取 el-select-v2 的输入框并调用 blur()
    const input = selectItemRef.value?.$el?.querySelector('input');
    if (input) {
      input.blur();
    }
    // 延迟执行移除操作，确保失去焦点完成
    setTimeout(() => {
      const index = state.itemOpts.indexOf(row);
      if (index !== -1) {
        state.itemOpts.splice(index, 1);
      }
    }, 10);
    openItemApi.DeleteByKey(row.value).then(async (rs) => {
      if (row.value == state.formTable.params.openItemId) {
        state.formTable.params.openItemId = state.itemOpts[0].value;
        await getOpenItemProject();
        await onSearch();
        await openItemApi.Detail(state.formTable.params.openItemId).then((rs: any) => {
          if (rs.data != null) {
            console.log(rs.data);
            state.formTable.params.customer = rs.data.customer;
            getTickets();
          }
        });
      }
      ElMessage.success('Successfully Deleted');
    });
  });
};

// 选中值变化前的处理逻辑
const onStatusChange = (newValue: string, row: any) => {
  const updateComment = () => {
    ElMessageBox.confirm(t('message.openItemDlgTips.PleaseUpdateComment'), t('message.page.dlgTip'), {
      confirmButtonText: t('message.page.buttonOk'),
      showCancelButton: false,
      type: 'warning',
    })
      .then(() => {
        // Handle confirm action
      })
      .catch(() => {
        // Handle cancel action
      });
  };

  if (newValue == 'Complete') {
    // 弹窗确认
    ElMessageBox.confirm(t('message.openItemDlgTips.dlgStatusCompleteText'), t('message.page.dlgTip'), {
      confirmButtonText: t('message.page.confirm'),
      cancelButtonText: t('message.page.cancel'),
      type: 'warning',
    })
      .then(() => {
        // 用户点击“确定”，更新选中的值
        updateComment();
      })
      .catch(() => {
        // 用户点击“取消”，不更新选中的值
        row.status = state.oldRow.status;
      });
  }
  else if (newValue == 'Cancelled') {
    // 弹窗确认
    ElMessageBox.confirm(t('message.openItemDlgTips.dlgStatusCancelledText'), t('message.page.dlgTip'), {
      confirmButtonText: t('message.page.confirm'),
      cancelButtonText: t('message.page.cancel'),
      type: 'warning',
    })
      .then(() => {
        // 用户点击“确定”，更新选中的值
        updateComment();
      })
      .catch(() => {
        // 用户点击“取消”，不更新选中的值
        row.status = state.oldRow.status;
      });
  } else {
    updateComment();
  }
};
const onProjectChange = (newValue: string, row: any) => {
  row.projectName = state.projectOpts.find((x: any) => x.value === newValue)?.label || '';
};
const onCreateItemInit = (id: string) => {
  getOpenItemDetails(async () => {
    await getOpenItemProject();
    await onSearch();
    await getTickets();
  }, id);
};
const onSearch = async () => {
  state.formTable.loading = true;
  var obj = { ...state.formTable.params };
  if (!parseThanZero(obj.openItemId)) {
    obj.openItemId = '0';
  }
  await openItemDetailApi
    .Query(obj)
    .then((rs: any) => {
      state.formTable.data = rs.data;
      state.formTable.total = rs.totalCount;
      // option.value = rs;
      if (state.formTable.data.length > 0) {
        state.formTable.data.forEach((item: any) => {
          item.relatedTicketIds = item.relatedTickets.map((ticket: any) => ticket.ticketId);
          item.relatedTicketNumbers = item.relatedTickets.map((ticket: any) => ticket.ticketNumber).join(',');
          item.openDate = formatDay(item.openDate);
          item.closeDate = formatDay(item.closeDate);
          item.approvedDate = formatDay(item.approvedDate);
          item.loe = item.loe ? String(item.loe) : '';
          item.projectName = item.project == 0 ? '' : state.projectOpts.find((x: any) => x.value === item.project)?.label || item.project;
        });
        Object.keys(state.formTable.data[0]).forEach((key) => {
          const columnData = state.formTable.data.map((item: any) => item[key]);
          let counts: Record<string, number> = {};
          if (key === 'relatedTickets') {
            counts = columnData.reduce((acc: Record<string, number>, value: any) => {
              const combinedKey = Array.isArray(value) ? value.map((x: any) => x.ticketNumber).join(',') : value;
              return {
                ...acc,
                [combinedKey]: (acc[combinedKey] || 0) + 1,
              };
            }, {});
          } else {
            counts = columnData.reduce(
              (acc: Record<string, number>, value: any) => ({
                ...acc,
                [value]: (acc[value] || 0) + 1,
              }),
              {}
            );
          }
          if (key === 'loe') {
            console.log(counts);
          }
          // 直接使用 key 赋值
          uniqueValuesCounts[key] = Object.keys(counts).map((value) => ({
            value: value,
            count: counts[value],
          }));
        });
        console.log(uniqueValuesCounts);
        state.dataColumns.ticketList = uniqueValuesCounts.ticketNumber
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.priorityList = uniqueValuesCounts.priority
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.projectList = uniqueValuesCounts.projectName
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' || item.value === '0' ? '(Blanks)' : state.projectOpts.find((x: any) => x.value === item.value)?.label || item.value
              } `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.taskCategoryList = uniqueValuesCounts.taskCategory
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.descriptionList = uniqueValuesCounts.description
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.commentList = uniqueValuesCounts.comment
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.statusList = uniqueValuesCounts.status
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.openDateList = uniqueValuesCounts.openDate
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.closeDateList = uniqueValuesCounts.closeDate
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.loeList = uniqueValuesCounts.loe
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value}`,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.approvedDateList = uniqueValuesCounts.approvedDate
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value}`,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.pointofContactList = uniqueValuesCounts.pointOfContact
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.updateList = uniqueValuesCounts.updates
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });
        state.dataColumns.relatedTicketList = uniqueValuesCounts.relatedTicketNumbers
          .map((item: any) => ({
            value: item.value,
            label: `${item.value === '' ? '(Blanks)' : item.value} `,
          }))
          .sort((a, b) => {
            if (a.label === '(Blanks)') return -1; // 将 (Blanks) 固定到第一个
            return a.label.localeCompare(b.label); // 其他项按字母顺序排序
          });

        // 数据处理完成后，恢复filter状态
        nextTick(() => {
          restoreFilterState();
        });
      }
    })
    .catch((rs: any) => {
      handleError(rs);
    })
    .finally(() => {
      state.formTable.loading = false;
      if (state.formTable.data.length <= 0) {
        //数据为空，清空表头的默认选中
        nextTick(() => {
          proxy.$refs.tableRef.clearCheckboxRow();
        });
      }
    });
};

const onSortChange = (column: any) => {
  if (!column.field) {
    if (column.column.sortable) {
      state.formTable.params.order = column.column.field;
      let field = column.column.field;
      let order: 'desc' | 'asc' = 'desc'; // 默认降序
      const $table = tableRef.value;
      if (state.formTable.params.sort == 'desc') {
        state.formTable.params.sort = 'asc';
        order = 'asc';
        if ($table) {
          $table.setSort({ field, order });
        }
      } else {
        state.formTable.params.sort = 'desc';
        order = 'desc';
        if ($table) {
          $table.setSort({ field, order });
        }
      }
      onSearch();
    }
  } else {
    state.formTable.params.order = column.field;
    state.formTable.params.sort = column.order;
    onSearch();
  }
};
const onClear = () => {
  clearQueryParams(state.formTable.params);
  onSearch();
};
const onSave = () => {
  const $table = tableRef.value;
  if ($table) {
    // 还原数据
    $table.clearEdit();
    const updateRecords = $table.getUpdateRecords();

    Promise.all(
      updateRecords.map((record) =>
        openItemDetailApi
          .Save(record)
          .then((res) => { })
          .catch((rs: any) => {
            handleError(rs);
          })
      )
    )
      .then((results) => {
        ElMessage.success('Saved Successfully');
        onSearch();
      })
      .catch((error) => { });

    /* 	for (var record of updateRecords) {
      openItemDetailApi
        .Save(record)
        .then((res) => {})
        .catch((rs: any) => {
          allSavedSuccessfully = false;
          ElMessage.error(rs.resultMsg || rs.toString());
        })
        .finally(() => {
          //刷新，固定列无法自适应高度
          onSearch();
        });
    }
    if (allSavedSuccessfully) {
      ElMessage.success('Saved Successfully');
    } */
  }
};
const onCancel = () => {
  const $table = tableRef.value;
  if ($table) {
    const updateRecords = $table.getUpdateRecords();
    $table.clearEdit();
    // 还原数据
    updateRecords.forEach((item: any) => {
      $table.revertData(item);
    });

    //    $table.refreshScroll()
  }
  // onSearch();
};

function clearQueryParams(params: any): void {
  Object.assign(params, getDefaultQueryParams());
}

const onSizeChange = (val: number) => {
  state.formTable.params.pageSize = val;
  onSearch();
};

const onCurrentChange = (val: number) => {
  state.formTable.params.pageIndex = val;

  onSearch();
};

const cellDblClick = ({ row, column }: any) => {
  //记录旧值row
  state.oldRow = { ...row };
  if (row.status == 'Complete' || row.status == 'Cancelled') {
    state.isStatusDisabled = true;
  } else {
    state.isStatusDisabled = false;
  }
};

// 计算属性函数
const getComputedOpenDate = (row: any) => {
  return computed({
    get: () => {
      const year = getYearFromDate(row.openDate);
      return year !== '1970' ? row.openDate : undefined;
    },
    set: (newValue) => {
      row.openDate = newValue; // 更新 row.openDate
    },
  });
};

const getComputedDate = (date: any) => {
  return computed({
    get: () => {
      const year = getYearFromDate(date);

      if (!year || year === '1970' || year === '1969' || year === '0001' || year === '1900') {
        return undefined;
      }

      return date;
    },
  });
};

const onSelectChange = async (val: any) => {
  state.formTable.params.openItemId = val;
  await getOpenItemProject();
  await onSearch();
  await openItemApi.Detail(val).then((rs: any) => {
    if (rs.data != null) {
      console.log(rs.data);
      state.formTable.params.customer = rs.data.customer;
      getTickets();
    }
  });
};

const onExportAllRecord = (selectAll: number) => {
  var checkSelection = proxy.$refs.tableRef.getCheckboxRecords();
  let ids_arr = [] as any;

  checkSelection.forEach(function (item: any) {
    let idjson = {} as any;
    idjson = item.id;
    ids_arr.push(idjson);
  });

  state.formTable.params.ischeckType = selectAll;
  if (selectAll == 1 && ids_arr.length == 0) {
    ElMessage.error(t('message.userFields.SelectTheDataYouWantToExport'));
    return;
  }

  if (selectAll == 0) {
    state.formTable.params.ids = [];
    state.formTable.params.OpenItemDetailFilters = getOpenItemDetailFilters();
  } else {
    state.formTable.params.ids = ids_arr;
  }
  var fileName = '';
  openItemApi.Detail(state.formTable.params.openItemId).then((rs: any) => {
    if (rs.data != null) {
      fileName = rs.data.name;
    }
  });

  openItemDetailApi
    .Export(state.formTable.params)
    .then((rs) => {
      rs.name = fileName;
      downloadCallback(rs);
    })
    .catch((rs) => { })
    .finally(() => {
      state.formTable.loading = false;
      state.formTable.params.ids = [];
    });

  // state.formTable.params.pageSize = currPageSize;
};

const downloadCallback = (rs: any) => {
  let data = rs;
  var newBlob = new Blob([data], { type: 'text/plain;charset=UTF-8' });
  var anchor = document.createElement('a');
  anchor.download = data.name + '_' + formatDateTime() + '.xlsx';
  anchor.href = window.URL.createObjectURL(newBlob);
  anchor.click();
};

const getOpenItemProject = async () => {
  state.projectOpts = [];
  await openItemProjectApi.Query({ openItemId: state.formTable.params.openItemId }).then((rs: any) => {
    if (rs.data.length > 0) {
      state.projectOpts = rs.data.map((item: any) => ({
        label: item.projectName,
        value: item.project,
      }));
    }
  });
};
//初始化加载
const onInitLoading = async () => {
  var dictArr = ['Priority', 'OpenItem Status', 'Task Category'];
  await dictItemApi.Many(dictArr).then((rs: any) => {
    state.priorityOpts = [];
    var items = rs.data?.find((a: any) => {
      return a.dictValue === 'Priority';
    })?.items;
    state.priorityOpts = items.map((item: any) => ({
      label: item.itemName,
      value: item.itemValue,
    }));

    state.statusOpts = [];
    var statusItems = rs.data?.find((a: any) => {
      return a.dictValue === 'OpenItem Status';
    })?.items;
    state.statusOpts = statusItems.map((item: any) => ({
      label: item.itemName,
      value: item.itemValue,
    }));

    state.taskCategoryOpts = [];
    var taskCategoryItems = rs.data?.find((a: any) => {
      return a.dictValue === 'Task Category';
    })?.items;
    if (taskCategoryItems) {
      state.taskCategoryOpts = taskCategoryItems.map((item: any) => ({
        label: item.itemName,
        value: item.itemValue,
      }));
    }
  });
};
const getOpenItemDetails = async (callback: () => void, id?: string) => {
  var params = {
    pageIndex: 1,
    pageSize: 100000,
    order: 'name',
    sort: 'asc',
  };
  await openItemApi
    .Query(params)
    .then((rs: any) => {
      if (rs.data.length > 0) {
        if (id) {
          const item = rs.data.find((a: any) => a.id === id);
          if (item) {
            state.formTable.params.openItemId = item.id;
            state.formTable.params.customer = item.customer;
          }
        } else {
          state.formTable.params.openItemId = rs.data[0].id;
          state.formTable.params.customer = rs.data[0].customer;
        }
        state.itemOpts = [];
        for (var item of rs.data) {
          state.itemOpts.push({ label: item.name, value: item.id });
        }
      }
    })
    .catch((rs: any) => {
      handleError(rs);
    })
    .finally(() => { });

  callback(); //执行回调函数
};

const getTickets = async () => {
  var params = {
    pageIndex: 1,
    pageSize: 100000,
    order: 'id',
    sort: 'asc',
    Ticket_Customer_ListV2: [] as any,
  };
  if (state.formTable.params.customer) {
    params.Ticket_Customer_ListV2.push(state.formTable.params.customer);
  }
  state.ticketLoading = true;
  await cmTicketsApi.QueryV2(params).then((rs: any) => {
    console.log(rs.data);
    state.ticketOpts = [];
    if (rs.data.length > 0) {
      rs.data.forEach((item: any) => {
        state.ticketOpts.push(processTicketOpts(item));
      });
    }
    state.ticketLoading = false;
  });
};
const processTicketOpts = (item: any) => {
  var ticketItem = {
    label: item.ticket_Number,
    value: item.id,
    children: [] as any,
  };

  if (item.children) {
    item.children.forEach((child: any) => {
      // 递归调用processTicketOpts，并将返回值添加到ticketItem的children数组中
      ticketItem.children.push(processTicketOpts(child));
    });
  }

  return ticketItem;
};
</script>

<style scoped lang="scss">
@media (max-width: 800px) {
  ::v-deep .el-dialog {
    width: 100% !important;
  }
}

.el-header.table_header {
  border-bottom: 0px solid #e6e6e6;
}

/* textarea 样式调整 */
.textarea-container {
  position: relative;
  display: flex;
  align-items: flex-start;
}

::v-deep .descTextarea .el-textarea__inner {
  padding: 1px !important;
}

::v-deep .commentTextarea .el-textarea__inner {
  padding: 0 25px 0 1px !important;
}

/* textarea 里面图标调整 */
.custom-icon {
  position: absolute;
  right: 5px;
  cursor: pointer;
  color: #888;
  font-size: 20px;
  transition: color 0.3s;
}

/* 鼠标进入图标调整  */
.custom-icon:hover {
  color: #409eff;
}

// #region loading样式
.custom-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  /* 图标和文字之间的间距 */
}

.is-loading {
  animation: rotate 1s linear infinite;
  /* 旋转动画 */
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

// #endregion</style>


<style scoped>
/* Open Item List 容器样式 */
.open-item-list-container {
  height: calc(100vh - 120px);
  /* 减去顶部导航和其他元素的高度 */
  overflow: hidden;
}

.open-item-list-container .el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.open-item-list-container .el-card__body {
  height: 100%;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.open-item-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: visible;
  /* 移除overflow auto，让内容自然流动 */
}

.table-wrapper {
  flex: 1;
  overflow: auto;
  /* 让表格容器可滚动 */
  min-height: 0;
  /* 确保flex子元素可以收缩 */
  padding: 10px 0;
}

/* 确保表格能够正确显示所有数据 */
.table-wrapper .vxe-table {
  height: auto !important;
  min-height: auto !important;
}

.table-wrapper .vxe-table .vxe-table--body-wrapper {
  overflow: visible !important;
  max-height: none !important;
}

/* 全屏模式下的特殊样式 */
.open-item-content.fullscreen-mode {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 1000 !important;
  background-color: #fff !important;
}

/* 确保滚动条样式 */
.table-wrapper::-webkit-scrollbar {
  width: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.fullscreen-box {
  background-color: #fff;
}

::v-deep .vxe-button.size--mini.type--button {
  height: var(--vxe-ui-table-row-line-height) !important;
}

::v-deep .vxe-cell--title {
  margin-right: auto;
}

::v-deep .vxe-table--filter-wrapper {
  margin-left: 60px;
}

::v-deep .vxe-table--render-default .vxe-header--column .vxe-cell {
  padding-right: var(--vxe-ui-table-border-width);
  /* padding-left:var(--vxe-ui-table-border-width); */
}

::v-deep .vxe-table--filter-wrapper .vxe-table--filter-footer {
  display: none;
  visibility: hidden;
}

::v-deep .my-filter-content .my-fc-search .my-fc-search-top .vxe-input {
  width: 210px !important;
}
</style>



<style>
.my-filter-content {
  padding: 10px;
  user-select: none;
}

.my-filter-content .my-fc-search .my-fc-search-top {
  position: relative;
  padding: 5px 0;
}

.my-filter-content .my-fc-search .my-fc-search-content {
  padding: 2px 10px;
}

.my-filter-content .my-fc-search-empty {
  text-align: center;
  padding: 20px 0;
}

.my-filter-content .my-fc-search-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.my-filter-content .my-fc-search-list-body {
  overflow: auto;
  max-height: var(--vxe-ui-upload-image-wh-default);
  max-width: var(--vxe-ui-table-menu-item-width);
}

.my-filter-content .my-fc-search-list .my-fc-search-item {
  padding: 2px 0;
  display: block;
}

.my-filter-content .my-fc-footer {
  border-top: 1px solid var(--vxe-ui-base-popup-border-color);
  text-align: right;
  padding-top: 2px;
}

.my-filter-content .my-fc-footer button {
  padding: 0 15px;
  margin-left: 15px;
}

.vxe-checkbox .vxe-checkbox--icon {
  color: var(--vxe-ui-base-popup-border-color);
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

/* Task Category 下拉框样式 */
.task-category-select {
  width: 150px !important;
  min-width: 150px !important;
}

/* Task Category 下拉框选项列表样式 */
::v-deep .task-category-select .el-select-v2__wrapper {
  width: 150px !important;
  min-width: 150px !important;
}

::v-deep .task-category-select .el-select-dropdown {
  min-width: 150px !important;
}

::v-deep .task-category-select .el-select-dropdown__item {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
}
</style>